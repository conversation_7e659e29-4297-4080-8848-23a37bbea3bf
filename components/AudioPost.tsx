'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { AudioPlayer } from './AudioPlayer'
import { AudioRecorder } from './AudioRecorder'
import { AudioRepliesList } from './AudioRepliesList'
import { Button } from './ui/button'
import { Heart, MessageCircle, MoreHorizontal } from 'lucide-react'
import { Day1Badge } from './Day1Badge'
import { createSupabaseClient } from '@/lib/supabase/client'

interface AudioPost {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  love_count: number
  reply_count: number
  created_at: string
  reactions?: Record<string, number>
  userReaction?: string | null
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
}

interface AudioPostProps {
  post: AudioPost
  currentUserId?: string
  isFollowing?: boolean

  onLove?: (postId: string) => void
  onReply?: (postId: string) => void
  onUserClick?: (userId: string) => void
}

export function AudioPost({
  post,
  currentUserId,
  isFollowing = false,

  onLove,
  onReply,
  onUserClick
}: AudioPostProps) {
  const [showReplies, setShowReplies] = useState(false)
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)
  const [showReactionPicker, setShowReactionPicker] = useState(false)
  const [reactions, setReactions] = useState(post.reactions || {})
  const [userReaction, setUserReaction] = useState(post.userReaction)
  const supabase = createSupabaseClient()

  const REACTION_TYPES = [
    { id: 'love', emoji: '❤️', label: 'Love' },
    { id: 'fire', emoji: '🔥', label: 'Fire' },
    { id: 'smile', emoji: '😊', label: 'Happy' },
    { id: 'cry', emoji: '😢', label: 'Sad' },
    { id: 'broken', emoji: '💔', label: 'Heartbroken' }
  ]

  const totalReactions = Object.values(reactions).reduce((sum, count) => sum + count, 0)
  const currentReactionEmoji = userReaction ? REACTION_TYPES.find(r => r.id === userReaction)?.emoji : null



  const handleReply = () => {
    setShowReplyRecorder(true)
  }

  const handleReaction = async (reactionId: string) => {
    if (!currentUserId) {
      alert('Please log in to react')
      return
    }

    setShowReactionPicker(false)

    try {
      // If user already has this reaction, remove it
      if (userReaction === reactionId) {
        await supabase
          .from('reactions')
          .delete()
          .eq('audio_post_id', post.id)
          .eq('user_id', currentUserId)

        setReactions(prev => ({
          ...prev,
          [reactionId]: Math.max(0, (prev[reactionId] || 0) - 1)
        }))
        setUserReaction(null)
      } else {
        // Remove old reaction if exists
        if (userReaction) {
          await supabase
            .from('reactions')
            .delete()
            .eq('audio_post_id', post.id)
            .eq('user_id', currentUserId)
        }

        // Add new reaction
        await supabase
          .from('reactions')
          .insert({
            audio_post_id: post.id,
            user_id: currentUserId,
            reaction_type: reactionId
          })

        setReactions(prev => ({
          ...prev,
          [reactionId]: (prev[reactionId] || 0) + 1,
          ...(userReaction ? { [userReaction]: Math.max(0, (prev[userReaction] || 0) - 1) } : {})
        }))
        setUserReaction(reactionId)
      }
    } catch (error) {
      console.error('Error updating reaction:', error)
      alert('Failed to update reaction')
    }
  }

  const handleAudioReplyComplete = async (audioBlob: Blob, duration: number) => {
    try {
      console.log('Audio reply recorded:', { duration, size: audioBlob.size })

      // Step 1: Get upload URL for the reply
      const uploadResponse = await fetch('/api/audio/replies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          parentPostId: post.id,
          duration: Math.round(duration * 10) / 10
        })
      })

      if (!uploadResponse.ok) {
        throw new Error('Failed to get upload URL for reply')
      }

      const { uploadUrl, key, publicUrl } = await uploadResponse.json()

      // Step 2: Upload the audio file to R2
      const uploadResult = await fetch(uploadUrl, {
        method: 'PUT',
        body: audioBlob,
        headers: {
          'Content-Type': 'audio/webm'
        }
      })

      if (!uploadResult.ok) {
        throw new Error('Failed to upload audio reply')
      }

      // Step 3: Save the reply to the database
      const createResponse = await fetch('/api/audio/replies/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          parentPostId: post.id,
          audioUrl: publicUrl,
          audioKey: key,
          duration: Math.round(duration * 10) / 10,
          description: '' // Could add description input later
        })
      })

      if (!createResponse.ok) {
        throw new Error('Failed to save audio reply')
      }

      // Success!
      setShowReplyRecorder(false)

      // Refresh the page or update the reply count
      window.location.reload() // Simple refresh for now

    } catch (error) {
      console.error('Error posting audio reply:', error)
      alert('Failed to post audio reply. Please try again.')
    }
  }

  const handleUserClick = () => {
    if (onUserClick) {
      onUserClick(post.user.id)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const postDate = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'now'
    if (diffInMinutes < 60) return `${diffInMinutes}m`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`
    return `${Math.floor(diffInMinutes / 1440)}d`
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 hover:shadow-md transition-shadow duration-200">
      {/* Header */}
      <div className="flex items-start gap-3 mb-3">
        <button
          onClick={handleUserClick}
          className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-gradient-to-br from-blue-100 via-cyan-50 to-purple-100 flex items-center justify-center overflow-hidden flex-shrink-0 hover:ring-4 hover:ring-blue-200/50 transition-all cursor-pointer shadow-md hover:shadow-lg hover:scale-105 duration-300"
        >
          {post.user.avatar || post.user.profile_picture_url ? (
            <Image
              src={(post.user.avatar || post.user.profile_picture_url) as string}
              alt={post.user.name || 'User avatar'}
              width={48}
              height={48}
              className="w-full h-full object-cover"
            />
          ) : (
            <span className="text-gray-500 text-lg">👤</span>
          )}
        </button>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <button
              onClick={handleUserClick}
              className="font-bold text-gray-900 hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text transition-all duration-300 truncate text-base sm:text-lg"
            >
              {post.user.name}
            </button>
            {post.user.has_day1_badge && (
              <Day1Badge
                signupNumber={post.user.signup_number}
                badgeTier={post.user.badge_tier}
                size="sm"
                className="flex-shrink-0"
              />
            )}
            <span className="text-gray-300 text-sm">•</span>
            <span className="text-gray-400 text-xs sm:text-sm font-medium">
              {formatTimeAgo(post.created_at)}
            </span>
          </div>

          {/* Audio indicator */}
          <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
            <div className="flex items-center gap-1 bg-gradient-to-r from-blue-100 to-cyan-100 px-2 py-1 rounded-full">
              <span className="text-blue-600 animate-pulse">🎵</span>
              <span className="font-semibold text-blue-700">Audio • {post.duration_seconds.toFixed(1)}s</span>
            </div>
            {isFollowing && (
              <div className="flex items-center gap-1 bg-gradient-to-r from-green-100 to-emerald-100 px-2 py-1 rounded-full">
                <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                <span className="text-xs font-bold text-green-700">Following</span>
              </div>
            )}
          </div>
        </div>

        <Button variant="ghost" size="sm" className="p-2">
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </div>

      {/* Description */}
      {post.description && (
        <div className="mb-3">
          <p className="text-gray-800 text-sm leading-relaxed">
            {post.description}
          </p>
        </div>
      )}

      {/* Audio Player */}
      <div className="mb-3">
        <AudioPlayer
          audioUrl={post.audio_url}
          duration={post.duration_seconds}
          className="w-full"
        />
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between mt-3">
        <div className="flex items-center gap-6">
          <div className="relative">
            <button
              onClick={() => setShowReactionPicker(!showReactionPicker)}
              disabled={!currentUserId}
              className={`group flex items-center gap-3 transition-all duration-300 hover:scale-105 min-h-[44px] ${
                userReaction
                  ? 'text-red-500'
                  : 'text-gray-600 hover:text-red-500'
              } ${!currentUserId ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <div className="relative">
                {currentReactionEmoji ? (
                  <span className="text-lg">{currentReactionEmoji}</span>
                ) : (
                  <Heart className="w-5 h-5 group-hover:animate-bounce" />
                )}
                <div className="absolute -inset-2 bg-red-100/50 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 -z-10 blur-sm"></div>
              </div>
              <span className="text-sm font-bold bg-gradient-to-r from-red-500 to-pink-500 bg-clip-text text-transparent group-hover:from-red-600 group-hover:to-pink-600">
                {totalReactions > 0 ? totalReactions : 'React'}
              </span>
            </button>

            {/* Reaction Picker Popup */}
            {showReactionPicker && (
              <div className="absolute bottom-full left-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg p-2 flex gap-1 z-10">
                {REACTION_TYPES.map((reaction) => (
                  <button
                    key={reaction.id}
                    onClick={() => handleReaction(reaction.id)}
                    className={`flex flex-col items-center p-2 rounded-lg hover:bg-gray-100 transition-colors min-w-[60px] ${
                      userReaction === reaction.id
                        ? 'bg-blue-50 border border-blue-200'
                        : ''
                    }`}
                    title={reaction.label}
                  >
                    <span className="text-xl mb-1">{reaction.emoji}</span>
                    <span className="text-xs text-gray-600 font-medium">
                      {reactions[reaction.id] || 0}
                    </span>
                  </button>
                ))}
              </div>
            )}

            {/* Click outside to close */}
            {showReactionPicker && (
              <div
                className="fixed inset-0 z-0"
                onClick={() => setShowReactionPicker(false)}
              />
            )}
          </div>

          <button
            onClick={handleReply}
            disabled={!currentUserId}
            className={`flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors min-h-[44px] ${
              !currentUserId ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <span className="text-sm">🎤</span>
            <span className="text-sm font-medium">Reply</span>
            {post.reply_count > 0 && (
              <span className="text-xs bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-2 py-1 rounded-full font-bold animate-pulse">
                {post.reply_count}
              </span>
            )}
          </button>
        </div>

        {/* Floating indicator + View replies */}
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowReplies(!showReplies)}
            className="group text-sm text-blue-600 hover:text-blue-700 font-bold transition-all duration-300 hover:scale-105 min-h-[44px] flex items-center gap-2"
          >
            <span>{showReplies ? 'Hide' : 'View'} replies</span>
            {post.reply_count > 0 && (
              <span className="text-xs bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-2 py-1 rounded-full font-bold animate-pulse">
                {post.reply_count}
              </span>
            )}
          </button>

          {/* Subtle floating indicator */}
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
            <div className="w-1 h-1 bg-cyan-300 rounded-full animate-pulse delay-100"></div>
            <div className="w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse delay-200"></div>
          </div>
        </div>
      </div>

      {/* Audio Reply Recorder */}
      {showReplyRecorder && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-blue-900">🎤 Record Audio Reply</h4>
            <button
              onClick={() => setShowReplyRecorder(false)}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Cancel
            </button>
          </div>
          <AudioRecorder
            maxDuration={9}
            onRecordingComplete={handleAudioReplyComplete}
            onCancel={() => setShowReplyRecorder(false)}
          />
        </div>
      )}

      {/* Audio Replies List */}
      {showReplies && (
        <AudioRepliesList
          postId={post.id}
          currentUserId={currentUserId}
          onReplyCountChange={(count) => {
            // Update the reply count in the post
            // This will be reflected in the UI
          }}
        />
      )}
    </div>
  )
}
